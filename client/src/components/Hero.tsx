import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import HeroBubbleBackground from "./HeroBubbleBackground";
import HeroBubbleBackgroundAlt from "./HeroBubbleBackgroundAlt";
import DashboardPreview from "./DashboardPreview";
import { fadeInUp, staggerContainer } from "@/lib/animations";

type HeroProps = {
  customTitle?: string;
  customSubtitle?: string;
  state?: "TX" | "FL" | "NY";
};

export default function Hero({
  customTitle,
  customSubtitle,
  state,
}: HeroProps) {
  // Always use the default bubble style (Style 1) as preferred
  const bubbleStyle = "default";

  const sectionRef = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  // Parallax values for different elements
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const contentY = useTransform(scrollYProgress, [0, 1], ["0%", "5%"]);

  // Animation variants for staggered fade-in
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  return (
    <section
      ref={sectionRef}
      className="relative min-h-screen pt-24 pb-16 flex items-center overflow-hidden"
    >
      {/* Diagonal gradient background */}
      <div className="absolute inset-0 z-0 bg-gradient-to-br from-[#F5F7FA] to-white overflow-hidden" />

      {/* Modern floating bubbles background - Direct implementation */}
      <motion.div
        className="absolute inset-0 z-[1] overflow-hidden"
        style={{ y: backgroundY }}
      >
        {bubbleStyle === "default" ? (
          <div className="relative w-full h-full">
            <motion.div
              className="absolute w-[300px] h-[300px] rounded-full bg-blue-100/20"
              style={{
                filter: "blur(30px)",
                left: "5%",
                bottom: "-50px",
                backgroundImage:
                  "linear-gradient(45deg, rgba(184, 217, 255, 0.7), rgba(245, 247, 250, 0.7))",
                boxShadow: "0 0 40px rgba(200, 220, 255, 0.4)",
              }}
              animate={{
                y: [0, -300, 0],
                x: [0, 20, 0],
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 38,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute w-[200px] h-[200px] rounded-full bg-blue-100/20"
              style={{
                filter: "blur(30px)",
                left: "20%",
                bottom: "-80px",
                backgroundImage:
                  "linear-gradient(135deg, rgba(230, 245, 252, 0.7), rgba(205, 232, 255, 0.7))",
                boxShadow: "0 0 40px rgba(200, 220, 255, 0.4)",
              }}
              animate={{
                y: [0, -400, 0],
                x: [0, -30, 0],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 32,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute w-[250px] h-[250px] rounded-full bg-blue-100/20"
              style={{
                filter: "blur(30px)",
                right: "20%",
                bottom: "-100px",
                backgroundImage:
                  "linear-gradient(45deg, rgba(220, 240, 255, 0.7), rgba(200, 230, 255, 0.7))",
                boxShadow: "0 0 40px rgba(200, 220, 255, 0.4)",
              }}
              animate={{
                y: [0, -350, 0],
                x: [0, 20, 0],
                scale: [1, 0.95, 1],
              }}
              transition={{
                duration: 35,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2,
              }}
            />
          </div>
        ) : (
          <div className="relative w-full h-full">
            <motion.div
              className="absolute w-[650px] h-[650px] rounded-full bg-blue-50/30"
              style={{
                filter: "blur(60px)",
                left: "-10%",
                top: "-20%",
                backgroundImage:
                  "radial-gradient(circle at center, rgba(230, 245, 252, 0.9), rgba(245, 247, 250, 0.6))",
                boxShadow: "0 0 60px rgba(200, 220, 255, 0.3)",
              }}
              animate={{
                x: [0, "5%"],
                y: [0, "10%"],
                scale: [1, 1.1],
              }}
              transition={{
                duration: 40,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute w-[400px] h-[400px] rounded-full bg-blue-50/30"
              style={{
                filter: "blur(60px)",
                right: "-5%",
                top: "10%",
                backgroundImage:
                  "radial-gradient(circle at center, rgba(218, 235, 252, 0.8), rgba(245, 247, 250, 0.5))",
                boxShadow: "0 0 60px rgba(200, 220, 255, 0.3)",
              }}
              animate={{
                x: [0, "-5%"],
                y: [0, "15%"],
                scale: [1, 0.9],
              }}
              transition={{
                duration: 35,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute w-[500px] h-[500px] rounded-full bg-blue-50/30"
              style={{
                filter: "blur(60px)",
                left: "30%",
                bottom: "-20%",
                backgroundImage:
                  "radial-gradient(circle at center, rgba(230, 245, 252, 0.7), rgba(245, 247, 250, 0.4))",
                boxShadow: "0 0 60px rgba(200, 220, 255, 0.3)",
              }}
              animate={{
                x: [0, "8%"],
                y: [0, "-10%"],
                scale: [1, 1.05],
              }}
              transition={{
                duration: 38,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut",
              }}
            />
          </div>
        )}
      </motion.div>

      {/* Blueprint background with parallax */}
      <motion.div
        className="absolute inset-0 z-0 opacity-5 overflow-hidden"
        style={{ y: backgroundY }}
      >
        <motion.svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full opacity-10"
          viewBox="0 0 1000 1000"
          initial={{ scale: 0.95 }}
          animate={{ scale: 1.05 }}
          transition={{
            duration: 30,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        >
          <g fill="none" stroke="#1A365D" strokeWidth="1">
            <path d="M100,100 h800 v800 h-800 z" />
            <path d="M200,200 h600 v600 h-600 z" />
            <path d="M300,300 h400 v400 h-400 z" />
            <path d="M0,0 l1000,1000" />
            <path d="M1000,0 l-1000,1000" />
          </g>
        </motion.svg>
      </motion.div>

      {/* Content with parallax and elevated z-layer */}
      <motion.div
        className="container-full relative z-10"
        style={{ y: contentY }}
      >
        <div className="flex flex-col lg:flex-row">
          <motion.div
            className="lg:w-3/5 mb-10 lg:mb-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.7 }}
          >
            <motion.div
              className="p-6 sm:p-8 md:p-10 lg:p-12 rounded-xl shadow-[0px_10px_30px_rgba(0,0,0,0.04)] bg-white/80 backdrop-blur-sm max-w-3xl"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <motion.h1
                className="text-3xl sm:text-4xl md:text-5xl font-bold leading-tight mb-5 text-navy"
                variants={itemVariants}
              >
                {customTitle ||
                  "Your AI Legal Associate—Works While You're in Court."}
              </motion.h1>

              {/* Subtitle Block */}
              <motion.div
                className="max-w-xl text-left"
                variants={itemVariants}
              >
                {/* First line - Responsive text sizing */}
                <p className="text-lg sm:text-xl text-gray-600 leading-relaxed mb-5">
                  {customSubtitle ||
                    "Research like a librarian, draft like an associate, organize like a paralegal — at solo-firm prices."}
                </p>

                {/* Second line with brush underline - using SVG directly with texture effect */}
                <div className="relative inline-block mb-6 text-center sm:text-left">
                  <p className="text-xl sm:text-2xl font-semibold text-gray-900 relative z-10 pb-1">
                    AiLex can do it ALL for you.
                  </p>
                  <div className="absolute bottom-1 left-0 right-0">
                    {/* Multiple overlapping SVG paths for a more natural brush texture */}
                    <div className="relative">
                      {/* Base brush stroke - increased opacity and stroke width */}
                      <svg
                        width="100%"
                        height="10"
                        viewBox="0 0 300 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        preserveAspectRatio="none"
                      >
                        <path
                          d="M5 10C40 5 90 14 150 10C210 6 260 14 295 10"
                          stroke="#B8FF5C"
                          strokeWidth="8"
                          strokeLinecap="round"
                          opacity="0.9"
                        />
                      </svg>

                      {/* Texture layer 1 - brighter color */}
                      <svg
                        className="absolute top-0 left-0"
                        width="100%"
                        height="10"
                        viewBox="0 0 300 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        preserveAspectRatio="none"
                      >
                        <path
                          d="M10 11C35 8 95 13 145 11C200 9 255 12 290 9"
                          stroke="#e4ffcc"
                          strokeWidth="3"
                          strokeLinecap="round"
                          opacity="0.7"
                        />
                      </svg>

                      {/* Texture layer 2 - increased visibility */}
                      <svg
                        className="absolute top-0 left-0"
                        width="100%"
                        height="10"
                        viewBox="0 0 300 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        preserveAspectRatio="none"
                      >
                        <path
                          d="M8 9C50 12 100 8 155 9C210 10 250 8 292 11"
                          stroke="#B8FF5C"
                          strokeWidth="4"
                          strokeDasharray="1 8"
                          strokeLinecap="round"
                          opacity="0.6"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Feature badges positioned as in the screenshot - Mobile responsive */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-8">
                  <div className="inline-flex items-center px-4 py-1.5 rounded-full bg-[#F5FAFF] shadow-sm">
                    <div className="w-5 h-5 rounded-full bg-[#1EAEDB] flex items-center justify-center mr-2">
                      <svg
                        className="w-3 h-3 text-white"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <span className="font-medium text-sm text-gray-800">
                      State-specific research
                    </span>
                  </div>

                  <div className="inline-flex items-center px-4 py-1.5 rounded-full bg-[#F5FAFF] shadow-sm">
                    <div className="w-5 h-5 rounded-full bg-[#1EAEDB] flex items-center justify-center mr-2">
                      <svg
                        className="w-3 h-3 text-white"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <span className="font-medium text-sm text-gray-800">
                      AI receptionist
                    </span>
                  </div>
                </div>
              </motion.div>

              {/* Feature tags - Existing features */}
              {/* Removed standalone badge */}

              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                variants={itemVariants}
              >
                <motion.a
                  href="/login"
                  className="inline-flex items-center justify-center rounded-md py-3 px-6 font-medium text-sm text-white bg-[#1EAEDB] hover:bg-[#189eca] shadow-sm transition-colors duration-200 w-full sm:w-auto"
                  whileHover={{ scale: 1.03 }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                >
                  Start 14-day free trial
                </motion.a>

                <motion.a
                  href="/demo"
                  className="inline-flex items-center justify-center rounded-md px-5 py-3 font-medium text-sm border border-[#1EAEDB] bg-white text-[#0C1C2D] hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#1EAEDB]/50 w-full sm:w-auto"
                  whileHover={{ scale: 1.03 }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                >
                  <svg
                    className="w-4 h-4 mr-2 text-[#1EAEDB]"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="1.5"
                    />
                    <path
                      d="M15.5 12L10 15.5V8.5L15.5 12Z"
                      fill="currentColor"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <span>Watch 90-sec demo</span>
                </motion.a>
              </motion.div>
            </motion.div>

            {/* Key benefits section - Mobile responsive layout */}
            <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-6 mt-7 mb-5 sm:mb-10 px-4">
              <div className="inline-flex items-center px-4 py-1.5 rounded-full bg-[#1a2332] shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#B8FF5C] flex items-center justify-center mr-2">
                  <svg
                    className="w-3 h-3 text-[#0C1C2D]"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span className="font-medium text-sm text-white tracking-wide">
                  Start immediately
                </span>
              </div>

              <div className="inline-flex items-center px-4 py-1.5 rounded-full bg-[#1a2332] shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#B8FF5C] flex items-center justify-center mr-2">
                  <svg
                    className="w-3 h-3 text-[#0C1C2D]"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span className="font-medium text-sm text-white tracking-wide">
                  No setup needed
                </span>
              </div>

              <div className="inline-flex items-center px-4 py-1.5 rounded-full bg-[#1a2332] shadow-sm">
                <div className="w-5 h-5 rounded-full bg-[#B8FF5C] flex items-center justify-center mr-2">
                  <svg
                    className="w-3 h-3 text-[#0C1C2D]"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span className="font-medium text-sm text-white tracking-wide">
                  No learning curve
                </span>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="lg:w-2/5 lg:pl-10 mt-8 lg:mt-0"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            style={{
              perspective: 1000,
              transformStyle: "preserve-3d",
            }}
          >
            <motion.div
              style={{
                transformStyle: "preserve-3d",
                transform: "translateZ(20px)",
              }}
              className="relative z-10"
            >
              <DashboardPreview state={state} />
            </motion.div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}
